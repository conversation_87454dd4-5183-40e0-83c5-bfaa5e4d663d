import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  Snackbar,
  Paper,
  Tabs,
  Tab,
  Divider,
  Button,
  useTheme,
  useMediaQuery
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import PaymentIcon from '@mui/icons-material/Payment';
import { PageHeader, LoadingSpinner, ErrorAlert } from '../components/common';
import {
  CustomerList,
  CustomerContractList,
  PaymentForm,
  SuccessNotification
} from '../components/payment';
import { customerPaymentService, customerService } from '../services';
import { Customer, CustomerContract, CustomerPayment } from '../models';

const CustomerPaymentPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State for tabs
  const [tabValue, setTabValue] = useState<number>(0);

  // State for customers
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');

  // State for contracts
  const [contracts, setContracts] = useState<CustomerContract[]>([]);
  const [selectedContract, setSelectedContract] = useState<CustomerContract | null>(null);
  const [remainingAmount, setRemainingAmount] = useState<number>(0);

  // State for payment form
  const [paymentFormOpen, setPaymentFormOpen] = useState<boolean>(false);

  // State for UI
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState<boolean>(false);

  // Load customers on initial render
  useEffect(() => {
    fetchCustomers();
  }, []);

  // Fetch all customers
  const fetchCustomers = async () => {
    setLoading(true);
    try {
      const result = await customerService.getAllCustomers();
      setCustomers(result);
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError('Đã xảy ra lỗi khi tải danh sách khách hàng');
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle customer search
  const handleSearch = async (term: string) => {
    setSearchTerm(term);
    if (!term.trim()) return;

    setLoading(true);
    try {
      // Tìm kiếm theo cả tên và số điện thoại
      const result = await customerPaymentService.searchCustomers(term, term);
      setCustomers(result);

      if (result.length === 0) {
        setError('Không tìm thấy khách hàng nào phù hợp');
      } else {
        setError(null);
      }
    } catch (err) {
      console.error('Error searching customers:', err);
      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');
    } finally {
      setLoading(false);
    }
  };

  // Handle customer selection
  const handleSelectCustomer = async (customer: Customer) => {
    setSelectedCustomer(customer);
    setTabValue(1); // Switch to contracts tab
    setLoading(true);
    setError(null);

    try {
      const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(customer.id!);
      setContracts(activeContracts);
    } catch (err) {
      console.error('Error fetching contracts:', err);
      setError('Đã xảy ra lỗi khi tải danh sách hợp đồng');
    } finally {
      setLoading(false);
    }
  };

  // Handle payment button click
  const handlePaymentClick = async (contract: CustomerContract) => {
    setSelectedContract(contract);
    setLoading(true);

    try {
      // Get the latest contract payment info
      const contractInfo = await customerPaymentService.getContractPaymentInfo(contract.id!);
      const remaining = await customerPaymentService.getRemainingAmountByContractId(contract.id!);

      setSelectedContract(contractInfo);
      setRemainingAmount(remaining);
      setPaymentFormOpen(true);
    } catch (err) {
      console.error('Error fetching contract payment info:', err);
      setError('Đã xảy ra lỗi khi tải thông tin thanh toán hợp đồng');
    } finally {
      setLoading(false);
    }
  };

  // Handle payment form close
  const handlePaymentFormClose = () => {
    setPaymentFormOpen(false);
  };

  // Handle payment form submit
  const handlePaymentSubmit = async (payment: CustomerPayment) => {
    // Prevent double submission with multiple checks
    if (loading) {
      console.log('Payment submission blocked: already loading');
      return;
    }

    // Enhanced duplicate prevention
    const now = Date.now();
    const lastSubmission = localStorage.getItem('lastPaymentSubmission');
    const submissionKey = `payment_${payment.customerContractId}_${payment.paymentAmount}_${payment.paymentMethod}`;
    const lastSubmissionKey = localStorage.getItem('lastPaymentSubmissionKey');

    // Prevent rapid successive submissions
    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {
      console.log('Payment submission blocked: too rapid (within 2 seconds)');
      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');
      return;
    }

    // Prevent duplicate payment submissions
    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {
      console.log('Payment submission blocked: duplicate payment detected');
      setError('Thanh toán tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');
      return;
    }

    // Mark submission time and key to prevent rapid resubmission and duplicates
    localStorage.setItem('lastPaymentSubmission', now.toString());
    localStorage.setItem('lastPaymentSubmissionKey', submissionKey);
    setLoading(true);
    setError(null);

    try {
      console.log('Submitting payment creation request...');
      await customerPaymentService.createPayment(payment);
      console.log('Payment created successfully');

      setSuccessMessage('Thanh toán thành công!');
      setPaymentFormOpen(false);
      setShowSuccessNotification(true);

      // Clear the submission timestamp and key on success
      localStorage.removeItem('lastPaymentSubmission');
      localStorage.removeItem('lastPaymentSubmissionKey');

      // Refresh contracts list to show updated payment information
      if (selectedCustomer) {
        const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(selectedCustomer.id!);
        setContracts(activeContracts);
      }

      // Set flag to trigger refresh in contracts list page
      localStorage.setItem('contractsListNeedsRefresh', 'true');
    } catch (err) {
      console.error('Error creating payment:', err);
      setError('Đã xảy ra lỗi khi tạo thanh toán');

      // Clear the submission timestamp on error to allow retry
      localStorage.removeItem('lastPaymentSubmission');
    } finally {
      setLoading(false);
    }
  };

  // Handle success message close
  const handleSuccessClose = () => {
    setSuccessMessage(null);
  };

  // Handle success notification close
  const handleSuccessNotificationClose = () => {
    setShowSuccessNotification(false);
  };

  // Handle back to customer list
  const handleBackToCustomers = () => {
    setTabValue(0);
  };

  return (
    <Box>
      <PageHeader
        title="Thanh toán hợp đồng khách hàng"
        subtitle="Quản lý thanh toán hợp đồng khách hàng thuê lao động"
      />

      {error && <ErrorAlert message={error} />}

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant={isMobile ? "fullWidth" : "standard"}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<PersonIcon />}
            label="Danh sách khách hàng"
            iconPosition="start"
          />
          {selectedCustomer && (
            <Tab
              icon={<PaymentIcon />}
              label="Hợp đồng cần thanh toán"
              iconPosition="start"
            />
          )}
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && (
            <CustomerList
              customers={customers}
              onSelectCustomer={handleSelectCustomer}
              onSearch={handleSearch}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              loading={loading}
            />
          )}

          {tabValue === 1 && selectedCustomer && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Khách hàng: {selectedCustomer.fullName}
                  {selectedCustomer.companyName && ` (${selectedCustomer.companyName})`}
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<PersonIcon />}
                  onClick={handleBackToCustomers}
                >
                  Quay lại danh sách
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />

              {loading ? (
                <LoadingSpinner />
              ) : (
                <CustomerContractList
                  contracts={contracts}
                  onPaymentClick={handlePaymentClick}
                />
              )}
            </>
          )}
        </Box>
      </Paper>

      <PaymentForm
        open={paymentFormOpen}
        contract={selectedContract}
        onClose={handlePaymentFormClose}
        onSubmit={handlePaymentSubmit}
        remainingAmount={remainingAmount}
        loading={loading}
      />

      <SuccessNotification
        open={showSuccessNotification}
        message="Thanh toán đã được ghi nhận thành công!"
        onClose={handleSuccessNotificationClose}
      />

      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleSuccessClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSuccessClose} severity="success" sx={{ width: '100%' }}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CustomerPaymentPage;
